package com.kanzhun.foundation.db.dao;

import android.database.Cursor;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.kanzhun.foundation.model.Patch;
import com.kanzhun.foundation.model.ReadStatus;
import com.kanzhun.foundation.model.message.MessageConstants;
import com.kanzhun.foundation.model.message.MessageRecord;

import java.util.List;

/**
 * 返回值不要用MessageRecord
 */
@Dao
public interface MessageDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertMessage(MessageRecord messageRecord);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertMessages(List<MessageRecord> messageRecords);

    @Query("UPDATE TB_MESSAGE SET withdraw = :withdrawStatus WHERE mid = :msgId")
    void removeMessage(long msgId, int withdrawStatus);

    @Query("UPDATE TB_MESSAGE SET withdraw = :withdrawStatus WHERE mid in (:msgIds)")
    void removeMessages(List<Long> msgIds, int withdrawStatus);

    @Query("DELETE FROM tb_message WHERE mid = :msgId")
    void removeMessage(long msgId);

    @Query("SELECT max(mid) FROM TB_MESSAGE WHERE type =:type AND mid < " + MessageConstants.MSG_MID_STARTING_VALUE)
    long getMaxMsgId(int type);

    @Query("SELECT max(mid) FROM TB_MESSAGE")
    long getMaxMsgId();

    @Query("SELECT * FROM TB_MESSAGE WHERE chatId = :chatId AND type = :type AND seq >= :minSeq AND seq <= :maxSeq AND isShow = 1 ORDER BY seq DESC LIMIT :pageSize ")
    Cursor queryCursor(String chatId, int type, long minSeq, long maxSeq, int pageSize);

    @Query("SELECT * FROM TB_MESSAGE WHERE chatId = :chatId AND type = :type AND seq < :minSeq AND isShow = 1 ORDER BY seq DESC LIMIT :pageSize ")
    Cursor queryCursorEarlier(String chatId, int type, long minSeq, int pageSize);

    @Query("SELECT * FROM TB_MESSAGE WHERE chatId = :chatId AND type = :type AND seq > :maxSeq AND isShow = 1 ORDER BY seq LIMIT :pageSize ")
    Cursor queryCursorNew(String chatId, int type, long maxSeq, int pageSize);

    @Query("SELECT * FROM TB_MESSAGE WHERE chatId = :chatId AND type = :type AND seq >= :minSeq AND isShow = 1 ORDER BY seq LIMIT :pageSize ")
    Cursor queryCursorByMinSeq(String chatId, int type, long minSeq, int pageSize);

    @Query("SELECT * FROM TB_MESSAGE WHERE mid = :msgId LIMIT 1")
    Cursor query(long msgId);

    @Query("UPDATE TB_MESSAGE SET status = :status, mid = :newMid, seq = :seq WHERE mid = :oldMid")
    void updateState(long oldMid, int status, long newMid, long seq);

    @Query("UPDATE TB_MESSAGE SET status = :newStatus, seq = (SELECT max(a.seq) from TB_MESSAGE a where a.mid < " + MessageConstants.MSG_MID_STARTING_VALUE + " AND a.chatId = TB_MESSAGE.chatId AND a.type = TB_MESSAGE.type)  WHERE sender = :uid AND status = :oldStatus")
    int updateFailures(String uid, int oldStatus, int newStatus);

    @Query("SELECT * FROM TB_MESSAGE WHERE chatId = :chatId  AND type = :type AND withdraw = 0 AND seq >= :minSeq AND seq <= :maxSeq AND mediaType IN (" + MessageConstants.MSG_PIC + ")")
    Cursor queryPics(String chatId, int type, long minSeq, long maxSeq);

    @Query("SELECT * FROM TB_MESSAGE WHERE chatId = :chatId  AND type = :type AND withdraw = 0 AND mediaType = " + MessageConstants.MSG_FILE + " ORDER BY time DESC")
    Cursor queryFiles(String chatId, int type);

    @Query("SELECT * FROM TB_MESSAGE WHERE chatId = :uid AND type = :type AND withdraw = 0 AND mediaType in (" + MessageConstants.MSG_VIDEO + ", " + MessageConstants.MSG_PIC + ")")
    Cursor queryPicAndVideo(String uid, int type);

    @Query("SELECT * FROM TB_MESSAGE WHERE mid IN (:ids)")
    Cursor queryChatRecord(List<Long> ids);

    @Query("UPDATE TB_MESSAGE SET status=:status WHERE  sender=:uid AND chatId = :chatId AND type=:type AND chatId=:chatId AND mid<=:mid AND status IN (" + MessageConstants.MSG_STATE_DELIVERY + "," + MessageConstants.MSG_STATE_PART_READ + ")")
    int updateRead(String uid, String chatId, long mid, int type, int status);

    @Query("UPDATE TB_MESSAGE SET status=:status WHERE  sender!=:uid AND chatId = :chatId AND type=:type AND chatId=:chatId AND mid<=:mid AND status = " + MessageConstants.MSG_STATE_DELIVERY)
    void updateSyncRead(String uid, String chatId, long mid, int type, int status);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertRead(ReadStatus readStatus);

    @Query("SELECT * FROM TB_MSG_READ")
    List<ReadStatus> queryReads();

    @Delete
    void deleteRead(ReadStatus readStatus);

    @Delete
    void deleteReads(List<ReadStatus> readStatus);

    @Query("SELECT mid FROM TB_MESSAGE WHERE chatId =:chatId and sender!=:selfId and type = :type ORDER BY mid desc LIMIT 1")
    long getMaxRecID(String chatId, String selfId, int type);

    @Query("select (select max(seq) + 1 from TB_MESSAGE where chatId =:chatId AND type = :type AND status > 0 and seq < a.seq) as beginid, (seq - 1) as endid from TB_MESSAGE as a where chatId =:chatId AND type = :type AND status > 0 and a.seq > (select max(seq) + 1 from TB_MESSAGE where chatId =:chatId AND type = :type  AND status > 0 and seq < a.seq) ORDER BY seq DESC")
    List<Patch> queryPatch(String chatId, int type);

    @Query("SELECT max(seq) from TB_MESSAGE where chatId =:chatId AND type = :type")
    long getChatMaxSeq(String chatId, int type);

    @Query("SELECT max(seq) from TB_MESSAGE where chatId =:chatId AND type = :type AND isShow = 1")
    long getChatMaxShowSeq(String chatId, int type);

    @Query("UPDATE TB_MESSAGE SET isShow = 0, badged = 0 WHERE mid IN (:hideIds)")
    void hideMessages(List<Long> hideIds);

    @Query("SELECT min(seq) from TB_MESSAGE where chatId =:chatId AND type = :type AND isShow = 1")
    long getChatMinShowSeq(String chatId, int type);

    @Query("SELECT count(seq) from TB_MESSAGE where chatId =:chatId AND type = :type AND seq = :seq")
    int findChatBySeq(long chatId, int type, long seq);

//    @Query("UPDATE TB_MESSAGE SET replyContent = :summary, replySender = :sender WHERE replyId = :mid")
//    void updateReply(long mid, String summary, long sender);

    @Query("SELECT * FROM TB_MESSAGE WHERE chatId = :chatId AND type = :type AND seq >= :minSeq AND seq < :maxSeq AND isShow = 1 ORDER BY seq DESC LIMIT :pageSize ")
    Cursor queryTargetDurationMessages(long chatId, int type, long minSeq, long maxSeq, int pageSize);

    @Query("SELECT * FROM TB_MESSAGE WHERE chatId = :chatId AND type = :type AND seq <= :seq AND isShow = 1 ORDER BY seq DESC LIMIT :pageSize ")
    Cursor queryTargetMessages(long chatId, int type, long seq, int pageSize);

    @Query("SELECT mid FROM TB_MESSAGE WHERE transformer > 0")
    List<Long> queryObsoleteMessage();
    //AND version < " + BuildConfig.VERSION_CODE//

    @Query("SELECT mid FROM TB_MESSAGE WHERE mediaType = -1")
    List<Long> queryUpdateMessage();

    @Query("SELECT * FROM TB_MESSAGE WHERE chatId = :chatId AND type = :type AND seq < :startSeq AND isShow = 1 ORDER BY seq DESC LIMIT 1")
    Cursor queryForwardMessage(long chatId, int type, long startSeq);

    @Query("SELECT count(seq) from TB_MESSAGE where chatId =:chatId AND type = :type limit 1")
    int getChatCount(String chatId, int type);

    @Query("SELECT * FROM TB_MESSAGE WHERE chatId = :chatId AND type = :type AND isShow = 1 ORDER BY seq DESC LIMIT 1")
    Cursor getMaxShowMessage(String chatId, int type);

    @Query("SELECT * FROM TB_MESSAGE WHERE mid IN (:ids) ORDER BY seq LIMIT :size")
    Cursor queryChatRecordOrder(List<Long> ids, int size);

    @Query("DELETE FROM TB_MESSAGE WHERE mid = :msgId")
    void deleteMessage(long msgId);

    @Query("DELETE FROM TB_MESSAGE")
    void deleteAll();

    @Query("UPDATE TB_MESSAGE SET isShow = 0, badged = 0 WHERE chatId = :chatId AND type = :type AND seq <= :seq")
    void hideMessages(String chatId, int type, long seq);

    @Query("SELECT min(seq) FROM TB_MESSAGE WHERE chatId = :chatId AND type = :type")
    long queryMinExistSeq(String chatId, int type);

    @Query("DELETE FROM TB_MESSAGE WHERE chatId = :chatId AND type = :type AND seq <= :seq")
    void clearMessages(String chatId,int type ,long seq);
    @Query("SELECT * FROM TB_MESSAGE WHERE chatId = :chatId AND type = :type AND isShow = 1 ORDER by mid DESC LIMIT 1")
    Cursor getLatestShowMessage(String chatId,int type );
    @Query("SELECT * FROM TB_MESSAGE WHERE chatId = :chatId AND isShow = 1 ORDER by mid DESC LIMIT 1")
    Cursor getLatestShowMessage(String chatId);
}
